package com.pcitc.geodrawing.adapter.web;


import com.pcitc.geodrawing.domain.dto.persistence.DrawingFileDto;
import com.pcitc.geodrawing.domain.dto.persistence.RecognizeTaskDto;
import com.pcitc.geodrawing.domain.entity.persistence.DrawingFile;
import com.pcitc.geodrawing.domain.service.IDrawingFileService;
import com.pcitc.geodrawing.domain.vo.persistence.DrawingFileUploadVo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 图纸文件Controller
 * 
 * <AUTHOR>
 * @date 2025-07-10
 */
@Slf4j
@RestController
@RequestMapping("/geodrawing/file")
public class DrawingFileController extends BaseController
{
    @Autowired
    private IDrawingFileService drawingFileService;

    /**
     * 查询图纸文件列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DrawingFile drawingFile)
    {
        startPage();
        List<DrawingFile> list = drawingFileService.selectDrawingFileList(drawingFile);
        return getDataTable(list);
    }

    /**
     * 导出图纸文件列表
     */
    @Log(title = "图纸文件", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DrawingFile drawingFile)
    {
        List<DrawingFile> list = drawingFileService.selectDrawingFileList(drawingFile);
        ExcelUtil<DrawingFile> util = new ExcelUtil<>(DrawingFile.class);
        util.exportExcel(response, list, "图纸文件数据");
    }

    /**
     * 获取图纸文件详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(drawingFileService.selectDrawingFileById(id));
    }

    /**
     * 新增图纸文件
     */
    @Log(title = "图纸文件", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DrawingFile drawingFile)
    {
        return toAjax(drawingFileService.insertDrawingFile(drawingFile));
    }

    /**
     * 修改图纸文件
     */
    @Log(title = "图纸文件", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DrawingFile drawingFile)
    {
        return toAjax(drawingFileService.updateDrawingFile(drawingFile));
    }

    /**
     * 删除图纸文件
     */
    @Log(title = "图纸文件", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(drawingFileService.deleteDrawingFileByIds(ids));
    }

    /**
     * 上传图纸文件到对象存储
     *
     * @param drawingFile 图纸文件信息
     * @return 上传结果
     */
    @PostMapping("/uploadDrawingFile")
    @Log(title = "图纸文件", businessType = BusinessType.INSERT)
    public AjaxResult uploadDrawingFile(
            @RequestParam(value = "generalPlans", required = false) MultipartFile[] generalPlans,
            @RequestParam(value = "surveyReports", required = false) MultipartFile[] surveyReports,
            @ModelAttribute DrawingFileDto drawingFile) {
        DrawingFileUploadVo uploadVo = drawingFileService.uploadDrawingFile(generalPlans, surveyReports, drawingFile);
        return AjaxResult.success("图纸文件已上传，页面解析任务已提交，正在后台解析中", uploadVo);
    }

    /**
     * AI识别图纸图片
     *
     * @param recognizeTaskDtos 识别任务
     * @return 任务提交结果（不代表任务执行成功）
     */
    @PostMapping("/recognizeDrawingPages")
    @Log(title = "图纸文件", businessType = BusinessType.INSERT)
    public AjaxResult recognizeDrawingPages(@RequestBody List<RecognizeTaskDto> recognizeTaskDtos) {
        List<String> taskIds = drawingFileService.recognizeDrawingPages(recognizeTaskDtos);
        if (CollectionUtils.isEmpty(taskIds)) {
            return error("图纸图片识别任务提交失败");
        }
        return AjaxResult.success("图纸图片识别任务已提交，正在后台处理中", taskIds);
    }

    /**
     * 查询 AI 检测结果
     *
     * @param id 图纸id
     * @return AI 检测结果
     */
    @GetMapping(value = "/queryDetectionResult/drawing/{id}")
    public AjaxResult queryDetectionResult(@PathVariable("id") Long id) {
        return success(drawingFileService.queryDetectionResult(id));
    }

    /**
     * 查询 AI 检测结果（返回列表）
     *
     * @param fileId 图纸文件id
     * @return AI 检测结果
     */
    @GetMapping(value = "/getResultByFileId/drawing/{fileId}")
    public AjaxResult getResultByFileId(@PathVariable("fileId") Long fileId) {
        return success(drawingFileService.getResultByFileId(fileId));
    }

    /**
     * 修补逻辑视图结果
     *
     * @param fileId 图纸文件id
     * @return 修补结果
     */
    @PostMapping(value = "/repairLogicVisionResult/{fileId}")
    public AjaxResult repairLogicVisionResult(@PathVariable("fileId") String fileId){
        return success(drawingFileService.repairLogicVisionResult(fileId));
    }

    /**
     * 审查图纸文件
     * @param layoutReportId 总图报告id
     * @param geoReportId 地勘报告id
     * @return
     */
    @PostMapping(value = "/investReport/{layoutReportId}/{geoReportId}")
    public AjaxResult investReport(@PathVariable Long layoutReportId, @PathVariable Long geoReportId) {
        return success(drawingFileService.investReport(layoutReportId, geoReportId));
    }
}
