package com.pcitc.geodrawing.domain.entity.recognition;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Difficult {
    private String id;
    private String anomalyType;
    private Double confidence;
    private String originText;
    private String optimizedText;
    private String notes;
}
